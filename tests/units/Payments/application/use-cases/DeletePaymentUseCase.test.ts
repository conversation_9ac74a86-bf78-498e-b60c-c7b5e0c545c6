import {
  FvNumber,
  NotFoundError,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { Payment } from '@/payments/domain/entities/Payment';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';

import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';

describe(`${DeletePaymentUseCase.name}`, () => {
  const paymentRepository = mock<PaymentRepository>();
  const useCase = new DeletePaymentUseCase(paymentRepository);

  const payment = PaymentMother.buildDefault();
  const deletePaymentDto = { paymentId: payment.id };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return NotFoundError when payment does not exist', async () => {
    const notFoundError = NotFoundError.build({
      context: 'PaymentMongoRepository',
      target: Payment.name,
    });

    paymentRepository.delete.mockResolvedValue(left(notFoundError));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);

    const errorResponse = useCaseResult.value as NotFoundError;

    expect(errorResponse.message).toStrictEqual(notFoundError.message);
  });

  it('should successfully delete payment when it exists', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(right(deletedCount));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    expect(useCaseResult.value).toBeInstanceOf(FvNumber);

    const deleteResult = useCaseResult.value as FvNumber;

    expect(deleteResult.toPrimitive()).toBe(1);
  });

  it('should call repository delete with correct criteria', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(right(deletedCount));

    await useCase.execute(deletePaymentDto);

    expect(paymentRepository.delete).toHaveBeenCalledTimes(1);

    // Verificar que se llama con el criterio correcto
    const calledCriteria = paymentRepository.delete.mock.calls[0][0];

    expect(calledCriteria).toBeDefined();
  });

  it('should handle different payment IDs correctly', async () => {
    const differentPaymentId = UniqueEntityID.create().toPrimitive();
    const differentDeleteDto = { paymentId: differentPaymentId };
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(right(deletedCount));

    const useCaseResult = await useCase.execute(differentDeleteDto);

    expect(useCaseResult.isRight()).toBeTruthy();
    expect(paymentRepository.delete).toHaveBeenCalledTimes(1);
  });
});
