import {
  Either2,
  Fv<PERSON><PERSON>ber,
  NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';

import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';

describe(`${DeletePaymentUseCase.name}`, () => {
  const paymentRepository = mock<PaymentRepository>();

  const useCase = new DeletePaymentUseCase(paymentRepository);

  const payment = PaymentMother.buildDefault();
  const deletePaymentDto = { paymentId: payment.id };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return NotFoundError when payment does not exist', async () => {
    const notFoundError = NotFoundError.build({
      context: 'PaymentMongoRepository',
      target: 'Payment',
    });

    paymentRepository.delete.mockResolvedValue(Either2.left(notFoundError));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should successfully delete payment when it exists', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(Either2.right(deletedCount));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const deleteResult = useCaseResult.get() as FvNumber;

    expect(deleteResult.toPrimitive()).toBe(1);
  });

  it('should call repository delete with correct criteria', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(right(deletedCount));

    await useCase.execute(deletePaymentDto);

    expect(paymentRepository.delete).toHaveBeenCalledTimes(1);

    // Verificar que se llama con el criterio correcto
    const calledCriteria = paymentRepository.delete.mock.calls[0][0];

    expect(calledCriteria).toBeDefined();
  });

  it('should handle different payment IDs correctly', async () => {
    const differentPaymentId = UniqueEntityID.create().toPrimitive();
    const differentDeleteDto = { paymentId: differentPaymentId };
    const deletedCount = FvNumber.build(1);

    paymentRepository.delete.mockResolvedValue(right(deletedCount));

    const useCaseResult = await useCase.execute(differentDeleteDto);

    expect(useCaseResult.isRight()).toBeTruthy();
    expect(paymentRepository.delete).toHaveBeenCalledTimes(1);
  });
});
